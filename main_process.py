import time
import os
from process_data import *
from generate_rr_entry import *
from device_configure import output_dir
from cubic_spline_interpolation_HV_mpy import *
from raw_scan_data_process import raw_scan_data_local_fitting

def main():
    """Main function for data processing workflow

    Args:
        output_dir: Output directory, default is 'output'
    """
    start_time = time.time()
    print("\033[1;34m" + "="*60 + "\033[0m")
    print("\033[1;34m NAND DATA PROCESSING WORKFLOW \033[0m")
    print("\033[1;34m" + "="*60 + "\033[0m")
    print("\033[1;36mStarting data processing...\033[0m")

    #########################################################
    #############  Parameter Setting  #######################
    #########################################################

    # 选择是否在本地使用最新算法处理原始数据
    local_process_fitting_data = True                   # 当测试时使用的fitting算法不是最终版时，需要在本地使用最新算法处理原始数据

    # 选择是否使用vendor提供的RR table生成初始offset组合
    all_offset_combo_from_vendor = False                # True: 使用vendor提供的RR table生成offset组合; False: 使用In-house算法生成offset组合
    if all_offset_combo_from_vendor:
        offset_case_num = 0                             # 当offset_case_num = 0时，使用全部vendor提供的offset组合; 当offset_case_num>0时，会根据vendor提供的offset组合，生成offset_case_num个offset组合
    else:
        offset_case_num = 21                             # 生成in-house RR，每个WLmode/WLtype/WLGroup/Partial_Prog/PageType生成offset组合的数量（包括全0）

    # 若是要生成In-house的offset组合，则需要修改以下参数
    # 1. 选择是否使用聚类方法生成offset组合
    generate_all_offset_combo_use_clustering = False     # True: 使用聚类方法生成offset组合; False: 根据bestoffset使用itertools.product生成offset组合
    # 2. 若是使用聚类方法，则需要选择聚类模式
    wl_clustering_mode = 'bestoffset_fbc_enhanced'                   # bestoffset（n个state_offset + WL）, bestoffset_fbc_enhanced（n个state_offset + 对应FBC）, fitting_data（n个state_offset + 对应FBC + FBC标准差）

    #########################################################
    ##############  Start Processing  #######################
    #########################################################
    try:
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        print(f"  \033[32mOutput directory: {output_dir}\033[0m")
        
        # # 1. Load data
        # print("\033[1;33mStep 1: Loading data\033[0m")
        # data = load_data()
        # if data.empty:
        #     print("    \033[31m❌ Failed to load valid data\033[0m")
        #     return

        # # 2. Data preprocessing, 返回的数据经过优化，内存占用更小
        # print("\033[1;33mStep 2: Preprocessing data\033[0m")
        # preprocessed_bestoffset_data, preprocessed_read_data, preprocessed_fitting_data = preprocess_data(data)

        # # 3. Process bestoffset (BestDAC)
        # print("\033[1;33mStep 3: Processing bestoffset\033[0m")
        # process_bestoffset(preprocessed_bestoffset_data)

        # # 4. Process read_data (FAILS4CHUNK)
        # print("\033[1;33mStep 4: Processing read_data\033[0m")
        # process_read_data(preprocessed_read_data)

        # # 5. Process fitting data and output the best data
        # print("\033[1;33mStep 5: Processing fitting data and output the best data\033[0m")
        # process_fitting_data(preprocessed_fitting_data)

        if local_process_fitting_data:
            # print("\033[1;33mStep 6: Processing Local_fitting_data with raw_scan_data_preprocessed.csv\033[0m")
            # raw_scan_data_local_fitting(os.path.join(output_dir, 'raw_scan_data_preprocessed.csv'), chunk_size=1000000)

            # 6. Process fitting_data and generate offset cases - using integrated version
            print("\033[1;33mStep 7: Processing Local_fitting_data with generate_offset_cases\033[0m")
            generate_cases_integrated(fitting_data_file='raw_scan_data_local_fitting.csv',
                                      bestoffset_data_file='raw_scan_data_local_bestoffset.csv',
                                      use_clustering = generate_all_offset_combo_use_clustering,
                                      clustering_mode = wl_clustering_mode,
                                      case_num = offset_case_num,
                                      all_offset_combo_from_vendor = all_offset_combo_from_vendor)

        else:
            print("\033[1;33mStep 6: Processing fitting_data with generate_offset_cases\033[0m")
            generate_cases_integrated(fitting_data_file='fitting_data.csv',
                                      bestoffset_data_file='bestoffset.csv',
                                      use_clustering = generate_all_offset_combo_use_clustering,
                                      clustering_mode = wl_clustering_mode,
                                      case_num = offset_case_num,
                                      all_offset_combo_from_vendor = all_offset_combo_from_vendor)

        # Processing complete
        elapsed_time = time.time() - start_time
        print(f"\033[1;32m✅ Processing complete, total time: {elapsed_time:.2f} seconds\033[0m")

    except Exception as e:
        print(f'\033[1;31m❌ Main process exception: {e}\033[0m')
        elapsed_time = time.time() - start_time
        print(f"\033[1;31m❌ Processing terminated abnormally, runtime: {elapsed_time:.2f} seconds\033[0m")

if __name__ == '__main__':

    main()
