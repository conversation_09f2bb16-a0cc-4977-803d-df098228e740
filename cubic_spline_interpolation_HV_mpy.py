#!/usr/bin/env python3
"""
平滑谷底的混合导数法 - MicroPython版本 (简化接口)

专为MicroPython环境设计，不依赖任何第三方库
仅使用Python标准库和基础数学函数

简化接口：只提供三个主要函数，与其他算法保持一致
- fit_curve_and_find_minimum_spline(x_data, y_data)
- find_min_cubic_spline(x_data, spline_params)  
- evaluate_spline(spline_params, x)
"""

def fit_curve_and_find_minimum_spline(x_data, y_data, valley_size=3):
    """
    主函数：实现完整的平滑谷底混合导数法
    
    参数:
    x_data: x坐标列表或数组
    y_data: y坐标列表或数组
    valley_size: 谷底区域大小，默认3个点
    
    返回:
    spline_params: 样条参数列表
    """
    n = len(x_data)
    if n < 2:
        return []
    
    # 按x值排序数据，原数据用字典保存可能不按顺序（python版本低），所以需要排序
    data_points = sorted(zip(x_data, y_data), key=lambda point: point[0])
    x_data = [point[0] for point in data_points]
    y_data = [point[1] for point in data_points]
    
    # 步骤一：计算PCHIP基准导数
    # 计算间距和斜率
    h = [x_data[i+1] - x_data[i] for i in range(n-1)]
    delta = [(y_data[i+1] - y_data[i]) / h[i] for i in range(n-1)]
    
    m = [0.0] * n
    
    if n == 2:
        # 只有两个点，线性插值
        m[0] = m[1] = delta[0]
    else:
        # 内部点的导数计算
        for i in range(1, n - 1):
            s1, s2 = delta[i-1], delta[i]
            
            # 检查是否为局部极值点
            if s1 * s2 <= 1e-10:
                m[i] = 0.0
            else:
                # Fritsch-Butland 加权调和平均
                h1, h2 = h[i-1], h[i]
                w1, w2 = 2 * h2 + h1, h2 + 2 * h1
                # m[i] = (w1 + w2) / (w1 / s1 + w2 / s2)    #当s1=1e-10时，w1/s1极大，单精度浮点数环境中（MicroPython 默认可能使用）得到的结果为0
                m[i] = (w1 + w2) * (s1 * s2) / (w1 * s2 + w2 * s1)  # 避免除零

        
        # 端点处理
        # 起始点
        h1, h2 = h[0], h[1]
        s1, s2 = delta[0], delta[1]
        m[0] = ((2 * h1 + h2) * s1 - h1 * s2) / (h1 + h2)
        
        # 保证单调性和约束
        if m[0] * s1 <= 0:
            m[0] = 0.0
        elif s1 * s2 > 0 and abs(m[0]) > 3 * abs(s1):
            m[0] = 3 * s1
        
        # 结束点
        h1, h2 = h[-1], h[-2]
        s1, s2 = delta[-1], delta[-2]
        m[n-1] = ((2 * h1 + h2) * s1 - h1 * s2) / (h1 + h2)
        
        # 保证单调性和约束
        if m[n-1] * s1 <= 0:
            m[n-1] = 0.0
        elif s1 * s2 > 0 and abs(m[n-1]) > 3 * abs(s1):
            m[n-1] = 3 * s1
    
    baseline_derivatives = m
    
    # 步骤二：找到谷底区域（改进：基于局部和最小的谷底选择算法）
    min_val = min(y_data)

    # 找到所有等于最小值的点
    min_indices = [i for i, y in enumerate(y_data) if y == min_val]

    # 选择最小值区间的中点作为基准点
    first_min_idx = min_indices[0]
    last_min_idx = min_indices[-1]
    min_idx = int((first_min_idx + last_min_idx) / 2)

    # 基于min_idx比较三种组合，选择最优的谷底区域
    combinations = [
        (min_idx-2, min_idx-1, min_idx, 0),    # 左偏组合
        (min_idx-1, min_idx, min_idx+1, 1),    # 中心组合（优先级最高）
        (min_idx, min_idx+1, min_idx+2, 2)     # 右偏组合
    ]

    best_sum = float('inf')
    valley_indices = None

    for combo in combinations:
        indices = combo[:3]
        priority = combo[3]

        # 检查边界条件
        if all(0 <= idx < len(y_data) for idx in indices):
            combo_sum = sum(y_data[idx] for idx in indices)

            if combo_sum < best_sum:
                best_sum = combo_sum
                valley_indices = list(indices)
            elif combo_sum == best_sum and priority == 1:
                # 和相同时，优先选择中间组合
                valley_indices = list(indices)

    # 如果没有找到有效组合，回退到原逻辑
    if valley_indices is None:
        half_size = valley_size // 2
        start_idx = max(0, min_idx - half_size)
        end_idx = min(n - 1, min_idx + half_size)

        # 确保至少有valley_size个点
        if end_idx - start_idx + 1 < valley_size and n >= valley_size:
            if start_idx == 0:
                end_idx = min(n - 1, start_idx + valley_size - 1)
            elif end_idx == n - 1:
                start_idx = max(0, end_idx - valley_size + 1)

        valley_indices = list(range(start_idx, end_idx + 1))
    
    # 步骤三：计算谷底区域的新导数
    x_valley = [x_data[i] for i in valley_indices]
    y_valley = [y_data[i] for i in valley_indices]
    
    n_valley = len(x_valley)
    
    # 构建最小二乘法的正规方程 A^T A x = A^T b
    # A = [x^2, x, 1], b = y
    
    # 计算 A^T A
    sum_x4 = sum(x**4 for x in x_valley)
    sum_x3 = sum(x**3 for x in x_valley)
    sum_x2 = sum(x**2 for x in x_valley)
    sum_x = sum(x_valley)
    sum_1 = n_valley
    
    # 计算 A^T b
    sum_x2y = sum(x**2 * y for x, y in zip(x_valley, y_valley))
    sum_xy = sum(x * y for x, y in zip(x_valley, y_valley))
    sum_y = sum(y_valley)
    
    # 构建矩阵和向量
    matrix = [
        [sum_x4, sum_x3, sum_x2],
        [sum_x3, sum_x2, sum_x],
        [sum_x2, sum_x, sum_1]
    ]
    vector = [sum_x2y, sum_xy, sum_y]
    
    # 解线性方程组（高斯消元法）
    aug = []
    for i in range(3):
        row = matrix[i][:] + [vector[i]]
        aug.append(row)
    
    # 高斯消元
    for i in range(3):
        # 寻找主元
        max_row = i
        for k in range(i + 1, 3):
            if abs(aug[k][i]) > abs(aug[max_row][i]):
                max_row = k
        
        # 交换行
        aug[i], aug[max_row] = aug[max_row], aug[i]
        
        # 消元
        for k in range(i + 1, 3):
            if abs(aug[i][i]) < 1e-10:
                continue
            factor = aug[k][i] / aug[i][i]
            for j in range(i, 4):
                aug[k][j] -= factor * aug[i][j]
    
    # 回代
    coeffs = [0.0] * 3
    for i in range(2, -1, -1):
        coeffs[i] = aug[i][3]
        for j in range(i + 1, 3):
            coeffs[i] -= aug[i][j] * coeffs[j]
        if abs(aug[i][i]) > 1e-10:
            coeffs[i] /= aug[i][i]
    
    a, b, c = coeffs[0], coeffs[1], coeffs[2]
    
    # 计算导数 y' = 2ax + b 在谷底各点的值
    valley_derivatives = []
    for idx in valley_indices:
        x = x_data[idx]
        derivative = 2 * a * x + b
        valley_derivatives.append(derivative)
    
    # 步骤四：创建混合导数集合
    hybrid_derivatives = baseline_derivatives[:]
    for i, idx in enumerate(valley_indices):
        hybrid_derivatives[idx] = valley_derivatives[i]
    
    # 步骤五：构造最终的三次埃尔米特样条
    # 计算三次Hermite样条多项式系数，与PCHIP_float格式一致
    spline_params = []

    for i in range(n - 1):
        x_i, x_i_plus_1 = x_data[i], x_data[i + 1]
        y_i, y_i_plus_1 = y_data[i], y_data[i + 1]
        m_i, m_i_plus_1 = hybrid_derivatives[i], hybrid_derivatives[i + 1]

        h_i = x_i_plus_1 - x_i
        delta_i = (y_i_plus_1 - y_i) / h_i

        # 使用与PCHIP_float相同的系数格式: a + b*dx + c*dx^2 + d*dx^3
        a = y_i
        b = m_i
        c = (3 * delta_i - 2 * m_i - m_i_plus_1) / h_i
        d = (m_i + m_i_plus_1 - 2 * delta_i) / (h_i**2)

        spline_params.append((a, b, c, d, x_i, x_i_plus_1))
    
    return spline_params


def find_min_cubic_spline(x_data, spline_params):
    """
    在样条曲线上搜索最小值，支持水平最小值区间的中点选择
    与PCHIP_float算法保持一致的实现风格

    改进：当存在水平最小值区间时，选择区间中点而非最左侧点，
    提供更好的稳健性和安全裕度来应对参数波动。

    性能优化：使用批量计算函数减少函数调用开销
    """
    # 在样条曲线上搜索最小值
    min_x = x_data[0]
    max_x = x_data[-1]
    step_size = 1  # 使用精细步长
    steps = int((max_x - min_x) / step_size)

    # 生成所有x坐标
    x_dense = [min_x + i * step_size for i in range(steps + 1)]

    # 优化：使用批量计算函数一次性计算所有y值（同时获取原始值和保护值）
    y_raw_values, y_dense = evaluate_spline_batch_optimized(spline_params, x_dense, return_both=True)

    # 第一步：找到真正的最小值（应用下限保护前）
    y_min_raw = min(y_raw_values)

    # 第二步：找到所有等于最小值的点（由于使用round(result,2)，可以直接比较相等）
    min_indices = []
    for i, y in enumerate(y_raw_values):
        if y == y_min_raw:  # 直接相等比较，因为都是round(result,2)的结果
            min_indices.append(i)

    # 第三步：选择最小值区间的中点
    if min_indices:
        # 计算中点索引（使用整数除法确保结果为整数）
        first_idx = min_indices[0]
        last_idx = min_indices[-1]
        center_idx = int((first_idx + last_idx) / 2)

        x_min = x_dense[center_idx]
        y_min = max(0, y_raw_values[center_idx])  # 使用中点位置的实际y值并应用下限保护
    else:
        # 回退到传统方法：找到最小值的第一个出现位置
        min_idx = y_raw_values.index(y_min_raw)
        x_min = x_dense[min_idx]
        y_min = max(0, y_min_raw)

    return x_min, y_min, x_dense, y_dense


def evaluate_spline(spline_params, x):
    """
    在给定点x评估三次样条函数，返回原始浮点数结果（不做向上取整）
    与PCHIP_float算法保持一致的实现风格
    优化：使用二分查找定位区间
    """
    # 快速处理边界情况
    if x <= spline_params[0][4]:  # x <= x_0
        a, b, c, d, x_i, _ = spline_params[0]
        dx = x - x_i
        result = ((d * dx + c) * dx + b) * dx + a
        return round(result,2)

    if x >= spline_params[-1][5]:  # x >= x_n
        a, b, c, d, x_i, _ = spline_params[-1]
        dx = x - x_i
        result = ((d * dx + c) * dx + b) * dx + a
        return round(result,2)

    # 二分查找定位区间
    left, right = 0, len(spline_params) - 1
    while left <= right:
        mid = (left + right) // 2
        _, _, _, _, x_i, x_i_plus_1 = spline_params[mid]

        if x_i <= x <= x_i_plus_1:
            a, b, c, d = spline_params[mid][:4]
            dx = x - x_i
            result = ((d * dx + c) * dx + b) * dx + a
            return round(result,2)
        elif x < x_i:
            right = mid - 1
        else:
            left = mid + 1

    # 如果没找到匹配区间（不应该发生）
    a, b, c, d, x_i, _ = spline_params[-1]
    dx = x - x_i
    result = ((d * dx + c) * dx + b) * dx + a
    return round(result,2)


def evaluate_spline_batch_optimized(spline_params, x_list, return_both=False):
    """
    批量评估样条函数的优化版本

    虽然仍使用Python循环，但通过以下优化减少开销：
    1. 预计算区间边界，减少重复访问
    2. 内联计算逻辑，减少函数调用
    3. 优化数据访问模式

    参数:
    spline_params: 样条参数列表
    x_list: x坐标列表
    return_both: 是否同时返回原始值和下限保护值，默认False

    返回:
    如果return_both=False: y_list (保留两位小数的列表)
    如果return_both=True: (y_raw_list, y_protected_list) 元组
    """
    if not spline_params:
        empty_list = [0.0] * len(x_list)
        return (empty_list, empty_list) if return_both else empty_list

    # 预提取边界信息以减少重复访问
    first_x_start = spline_params[0][4]
    last_x_end = spline_params[-1][5]
    first_params = spline_params[0]
    last_params = spline_params[-1]

    y_raw_list = []  # 原始值（保留两位小数）
    y_protected_list = []  # 应用下限保护的值

    for x in x_list:
        # 快速边界检查
        if x <= first_x_start:
            a, b, c, d, x_i = first_params[0], first_params[1], first_params[2], first_params[3], first_params[4]
            dx = x - x_i
            result = ((d * dx + c) * dx + b) * dx + a
            y_raw = round(result, 2)
            y_raw_list.append(y_raw)
            if return_both:
                y_protected_list.append(max(0, y_raw))
            continue

        if x >= last_x_end:
            a, b, c, d, x_i = last_params[0], last_params[1], last_params[2], last_params[3], last_params[4]
            dx = x - x_i
            result = ((d * dx + c) * dx + b) * dx + a
            y_raw = round(result, 2)
            y_raw_list.append(y_raw)
            if return_both:
                y_protected_list.append(max(0, y_raw))
            continue

        # 二分查找（内联优化）
        left, right = 0, len(spline_params) - 1
        found = False

        while left <= right:
            mid = (left + right) // 2
            param = spline_params[mid]
            x_i, x_i_plus_1 = param[4], param[5]

            if x_i <= x <= x_i_plus_1:
                a, b, c, d = param[0], param[1], param[2], param[3]
                dx = x - x_i
                result = ((d * dx + c) * dx + b) * dx + a
                y_raw = round(result, 2)
                y_raw_list.append(y_raw)
                if return_both:
                    y_protected_list.append(max(0, y_raw))
                found = True
                break
            elif x < x_i:
                right = mid - 1
            else:
                left = mid + 1

        # 回退处理（理论上不应该发生）
        if not found:
            a, b, c, d, x_i = last_params[0], last_params[1], last_params[2], last_params[3], last_params[4]
            dx = x - x_i
            result = ((d * dx + c) * dx + b) * dx + a
            y_raw = round(result, 2)
            y_raw_list.append(y_raw)
            if return_both:
                y_protected_list.append(max(0, y_raw))

    return (y_raw_list, y_protected_list) if return_both else y_raw_list
