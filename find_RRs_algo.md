```mermaid
graph TD
    A("Start: all_offset_combinations") --> B["Initialize states:<br/>- selected_RRs_list<br/>- selected_RRs_covered_WLs<br/>- selected_RRs_best_wl_fbc"];
    B --> C["Add DefaultRead to selected_RRs_list as baseline"];
    C --> D["Add DefaultRead's performance to selected_RRs_covered_WLs and selected_RRs_best_wl_fbc"];

    D --> E{"Loop until selected_RRs count is reached<br/>or no unselected RRs left"};
    E -- Loop --> F["For each unselected RRs, calculate:<br/>1. New Covered WLs (vs. selected_RRs_covered_WLs)<br/>2. FBC Improvement (vs. selected_RRs_best_wl_fbc)"];
    
    F --> G{"Any unselected RRs with<br/>New Coverage > 0?"};
    
    G -- Yes --> H["From unselected RRs with New Coverage > 0,<br/>select one with max FBC Improvement"];
    G -- No --> I["From unselected RRs,<br/>select one with max FBC Improvement"];
    
    H --> J["Add best RRs to selected_RRs_list"];
    I --> J;
    
    J --> K["Update states with best RR's performance:<br/>- selected_RRs_covered_WLs add best RR's covered_WL<br/>- selected_RRs_best_wl_fbc update the minFBC of all current selected RRs"];
    K --> E;
    
    E -- Done --> L["Calculate final union coverage statistics"];
    L --> M("End: Return selected_RRs_list and union_stats");
```
