"""
用于内存管理类

包含的类：
1. SharedDataManager - 共享内存数据管理器
2. PerformanceObjectPool - 性能对象池

这些类专门用于优化 NAND Flash 数据处理中的内存使用和性能。
"""

import numpy as np
import pandas as pd
import pickle
from multiprocessing import shared_memory


class SharedDataManager:
    """
    共享内存数据管理器
    优化多进程数据传输，避免大数据结构的重复序列化和反序列化
    """
    
    def __init__(self):
        self.shared_memories = []
        self.metadata = {}
    
    def create_shared_fbc_mapping(self, state_offset_wl_fbc_map):
        """
        创建共享的FBC映射数据
        
        Args:
            state_offset_wl_fbc_map: 原始的状态-偏移-WL-FBC映射字典
            
        Returns:
            str: 共享内存名称
        """
        try:
            # 序列化数据，将数据转换为字节流才能在共享内存中存储
            serialized_data = pickle.dumps(state_offset_wl_fbc_map)
            
            # 创建共享内存
            shm = shared_memory.SharedMemory(create=True, size=len(serialized_data))
            shm.buf[:len(serialized_data)] = serialized_data
            
            self.shared_memories.append(shm)
            self.metadata['fbc_mapping'] = {
                'name': shm.name,
                'size': len(serialized_data)
            }
            
            print(f"                \033[35m🧠 Created shared FBC mapping: {len(serialized_data)/1024/1024:.1f} MB\033[0m")
            return shm.name
            
        except Exception as e:
            print(f"                \033[31m❌ Error creating shared FBC mapping: {e}\033[0m")
            return None
    
    def create_shared_wl_idxs(self, pec_wl_idxs):
        """
        创建共享的WL键数据
        
        Args:
            pec_wl_keys: 原始的PEC-WL键映射字典
            
        Returns:
            str: 共享内存名称
        """
        try:
            serialized_data = pickle.dumps(pec_wl_idxs)
            
            shm = shared_memory.SharedMemory(create=True, size=len(serialized_data))
            shm.buf[:len(serialized_data)] = serialized_data
            
            self.shared_memories.append(shm)
            self.metadata['wl_idxs'] = {
                'name': shm.name,
                'size': len(serialized_data)
            }
            
            print(f"                \033[35m🧠 Created shared WL keys: {len(serialized_data)/1024:.1f} KB\033[0m")
            return shm.name
            
        except Exception as e:
            print(f"                \033[31m❌ Error creating shared WL keys: {e}\033[0m")
            return None

    def create_shared_pec_info_map(self, pec_info_map):
        """
        创建共享的PEC信息映射（包含每个PEC独立的WL映射）
        
        Args:
            pec_info_map: 包含每个PEC的WL映射和数量信息的字典
            
        Returns:
            str: 共享内存名称
        """
        try:
            serialized_data = pickle.dumps(pec_info_map)
            
            shm = shared_memory.SharedMemory(create=True, size=len(serialized_data))
            shm.buf[:len(serialized_data)] = serialized_data
            
            self.shared_memories.append(shm)
            self.metadata['pec_info_map'] = {
                'name': shm.name,
                'size': len(serialized_data)
            }
            
            print(f"                \033[35m🧠 Created shared PEC info map: {len(serialized_data)/1024/1024:.1f} MB\033[0m")
            return shm.name
            
        except Exception as e:
            print(f"                \033[31m❌ Error creating shared PEC info map: {e}\033[0m")
            return None
    
    def cleanup(self):
        """清理所有共享内存资源"""
        cleanup_count = 0
        for shm in self.shared_memories:
            try:
                shm.close()
                shm.unlink()
                cleanup_count += 1
            except Exception as e:
                print(f"                \033[33m⚠️  Warning: Failed to cleanup shared memory: {e}\033[0m")
        
        if cleanup_count > 0:
            print(f"                \033[32m✅ Cleaned up {cleanup_count} shared memory blocks\033[0m")
        
        self.shared_memories.clear()
        self.metadata.clear()


class PerformanceObjectPool:
    """
    性能对象池，减少重复创建performance字典的开销
    优化：复用字典对象，减少内存分配和GC压力
    """
    
    def __init__(self, pool_size=1000):
        self.pool = []
        self.pool_size = pool_size
        self.created_count = 0
        self.reused_count = 0
        
    def get_performance_dict(self):
        """获取性能字典对象"""
        if self.pool:
            obj = self.pool.pop()
            obj.clear()  # 清空但保留容量
            self.reused_count += 1
            return obj
        else:
            self.created_count += 1
            return {}
    
    def return_performance_dict(self, obj):
        """归还性能字典对象"""
        if len(self.pool) < self.pool_size and obj is not None:
            self.pool.append(obj)
    
    def get_stats(self):
        """获取对象池统计信息"""
        return {
            'created': self.created_count,
            'reused': self.reused_count,
            'pool_size': len(self.pool)
        }
