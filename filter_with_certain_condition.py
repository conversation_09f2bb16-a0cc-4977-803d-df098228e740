import pandas as pd
import os

if __name__ == "__main__":
    print("Start filtering data with certain condition")
    dtype= {
        # Low-cardinality string columns -> 'category'
        'Partial_Prog': 'category',
        'PEC': 'category',
        'WLmode': 'category',
        'WLType': 'category',
        'State': 'category',

        # High-cardinality or standard numeric columns -> Precise integer types
        'BlkRD': 'int8',
        'Temperature': 'float32',
        'Channel': 'int8',
        'Ce': 'int8',
        'Lun': 'int8',
        'Block': 'int32',      # CRITICAL: Must be integer to avoid groupby issues
        'Page': 'int32',       # CRITICAL: Must be integer to avoid groupby issues
        'WordLine': 'int32',
        'Level': 'int8',
        'WLGroup': 'int8',
        'Offset': 'int8',
        'FBC': 'float32',
    }
    fitting_data = pd.read_csv(os.path.join(r"/nandfile/hang/YMTC_X36070/2_BLKRD/output", 'raw_scan_data_local_fitting.csv'), dtype=dtype, low_memory=True)  # low_memory=True 可能会导致同一列在不同chunk中被推断为不同类型
    fitting_data = fitting_data[(fitting_data['BlkRD'] == 3) & (fitting_data['PEC'] == '4K') & (fitting_data['Block'] == 53) & (fitting_data['Level'] == 3) & (fitting_data['WordLine'] == 884)]
    fitting_data.to_csv(r"/nandfile/hang/YMTC_X36070/2_BLKRD/output/fitting_data_filtered.csv", index=False)