input_dir = '/home/<USER>/hang/YMTC_X36070/2_BLKRD/'
output_dir = '/home/<USER>/hang/YMTC_X36070/2_BLKRD/output'     #home/petaio or nandfile

# device basic information
device_name = "X36070"
FBC_criterion = 250
rel_test = "BlkRD"   # Partial+BlkRD; Partial+SPRD; Partial+DR; SPRD; BlkRD; DR: including HTDR or STDR(short term) or LTDR(long term) or RTDR(room temperture)
stress_col = rel_test.split('+')[-1]

if 'X36070' in device_name:
    plane_number = 4
    string_number = 6 
    blk_group = 5
    blk_freq = 380

    test_num_blk_per_pec = 4
    test_base_blk = {'0K': 20, '1K': 28, '2K': 36, '3K': 44, '4K': 52, '5K': 64}
    # '0K': 20, '1K': 28, '2K': 36, '3K': 44, '4K': 52, '5K': 64
    test_partial_prog_ratio = {'0_0_0':  ['10%','30%','50%','60%'],
                                '1_0_0':  ['80%','97%','99%','100%']}

    # QLC WordLine region division dictionary
    Inhouse_WLgroup = {
        0: (0, 65),    # Region 0: WordLine from 0 to 65
        1: (66, 320),  # Region 1
        2: (321, 479),  # Region 2
        3: (480, 870),  # Region 3
        4: (871, 1079),  # Region 4
        5: (1080, 1415)  # Region 5
    }

    Vendor_WLgroup = {
        0: (0, 1415),    # 0-34%
    }    

    # Vendor_WLgroup = {
    #     0: (0, 479),    # 0-34%
    #     1: (480, 851),  # 34-60%
    #     2: (852, 1079),  # 60-76%
    #     3: (1080, 1415)  # 76-100%
    # }

    # Vendor_WLgroup = {
    #     0: (0, 425),    # 0-30%
    #     1: (426, 851),  # 30-60%
    #     2: (852, 1133),  # 60-80%
    #     3: (1134, 1415)  # 80-100%
    # }

    bad_block = {'1_0_0_47'}

    # Get Level, WordLine, WLmode based on Page value
    def page_info(page):

        if page < 18:
            levels = 3 # TLC
            relative_page = page - 0
            layer_offset = 0

        elif page < 2706:
            levels = 4 # QLC
            relative_page = page - 18
            layer_offset = 1
        elif page < 2778:
            levels = 3 # TLC
            relative_page = page - 2706
            layer_offset = 113
        elif page < 5466:
            levels = 4 # QLC
            relative_page = page - 2778
            layer_offset = 117
        elif page < 5520:
            levels = 3 # TLC
            relative_page = page - 5466
            layer_offset = 229
        else:
            levels = 1 # SLC
            relative_page = page - 5520
            layer_offset = 232

        layer = relative_page // (levels * string_number) + layer_offset
        page_level = relative_page % levels
        wordline = relative_page // levels + layer_offset * string_number
        WLmode = 'TLC' if levels == 3 else ('QLC' if levels == 4 else 'SLC')

        return  layer, page_level, levels, wordline, WLmode