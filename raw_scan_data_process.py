import pandas as pd
import numpy as np
import os

from cubic_spline_interpolation_HV_mpy import *
from device_configure import stress_col

# 定义高效的数据处理函数 - 一次性完成样条插值和数据展开
def raw_scan_data_process_chunk(chunk_data, group_cols, required_cols):
    """优化的分块处理函数：样条插值 + 数据展开 + pivot操作一体化"""
    # 过滤需要的列
    df_filter = chunk_data[required_cols].copy()
    

    # 存储展开后的数据行
    expanded_rows = []
    bestoffset_rows = []

    # 按分组键分组
    grouped = df_filter.groupby(group_cols, observed=True)

    for group_key, group_data in grouped:
        # 检查是否包含vt_list和fail_list
        measurements = set(group_data['Measurement'].values)
        if not {'vt_list', 'fail_list'}.issubset(measurements):
            # 对于不完整的组，也要进行展开处理
            for _, row in group_data.iterrows():
                values = str(row['Value']).split(',')
                measurement = row['Measurement']

                # 创建基础行数据
                base_data = {col: row[col] for col in group_cols}

                # 为每个值创建单独的行
                for i, value in enumerate(values):
                    if value and value.strip():
                        row_data = base_data.copy()
                        row_data['index'] = i
                        row_data['Measurement'] = measurement
                        row_data['value'] = value.strip()
                        expanded_rows.append(row_data)
            continue

        try:
            # 获取vt_list和fail_list数据
            vt_row = group_data[group_data['Measurement'] == 'vt_list'].iloc[0]
            fail_row = group_data[group_data['Measurement'] == 'fail_list'].iloc[0]

            # 使用numpy快速解析数值
            vt_values = np.fromstring(vt_row['Value'], sep=',', dtype=float)
            fail_values = np.fromstring(fail_row['Value'], sep=',', dtype=float)

            if len(vt_values) != len(fail_values) or len(vt_values) == 0:
                # 处理异常情况，仍然展开原始数据
                for _, row in group_data.iterrows():
                    values = str(row['Value']).split(',')
                    measurement = row['Measurement']
                    base_data = {col: row[col] for col in group_cols}

                    for i, value in enumerate(values):
                        if value and value.strip():
                            row_data = base_data.copy()
                            row_data['index'] = i
                            row_data['Measurement'] = measurement
                            row_data['value'] = value.strip()
                            expanded_rows.append(row_data)
                continue

            # 排序
            sort_idx = np.argsort(vt_values)
            vt_sorted = vt_values[sort_idx]
            fail_sorted = fail_values[sort_idx]

            # 三次样条插值
            spline_params = fit_curve_and_find_minimum_spline(vt_sorted, fail_sorted)
            vt_min, state_fbc_min, x_dense, y_dense = find_min_cubic_spline(vt_sorted, spline_params)

            # group_cols + vt_min + state_fbc_min 保存为bestoffset_rows
            best_offset_row = {col: vt_row[col] for col in group_cols}
            best_offset_row['Offset'] = vt_min
            best_offset_row['FBC'] = state_fbc_min
            bestoffset_rows.append(best_offset_row)

            # 直接展开插值后的数据
            base_data = {col: vt_row[col] for col in group_cols}

            # 展开vt_list (重命名为Offset)
            for i, x_val in enumerate(x_dense):
                row_data = base_data.copy()
                row_data['index'] = i
                row_data['Measurement'] = 'vt_list'
                row_data['value'] = f'{x_val:.1f}'
                expanded_rows.append(row_data)

            # 展开fail_list
            for i, y_val in enumerate(y_dense):
                row_data = base_data.copy()
                row_data['index'] = i
                row_data['Measurement'] = 'fail_list'
                row_data['value'] = f'{y_val:.1f}'
                expanded_rows.append(row_data)

        except Exception as e:
            print(f"处理组 {group_key} 时出错: {str(e)}")
            # 出错时展开原始数据
            for _, row in group_data.iterrows():
                values = str(row['Value']).split(',')
                measurement = row['Measurement']
                base_data = {col: row[col] for col in group_cols}

                for i, value in enumerate(values):
                    if value and value.strip():
                        row_data = base_data.copy()
                        row_data['index'] = i
                        row_data['Measurement'] = measurement
                        row_data['value'] = value.strip()
                        expanded_rows.append(row_data)

    if not expanded_rows:
        return pd.DataFrame(), pd.DataFrame()

    # 创建展开后的DataFrame，清理中间数据
    exploded_data = pd.DataFrame(expanded_rows)
    del expanded_rows
    bestoffset_data = pd.DataFrame(bestoffset_rows)
    del bestoffset_rows

    # 进行pivot：按Measurement分离vt_list和fail_list
    pivot_data = exploded_data.pivot_table(
        index=group_cols + ['index'],
        columns='Measurement',
        values='value',
        aggfunc='first'
    ).reset_index()

    # 清理exploded数据
    del exploded_data

    # 重置列名并转换为数值类型
    pivot_data.columns.name = None
    for col in ['vt_list', 'fail_list']:
        if col in pivot_data.columns:
            pivot_data[col] = pd.to_numeric(pivot_data[col], errors='coerce') #pd.to_numeric() 会自动选择最合适的数值类型

    # 移除index列
    if 'index' in pivot_data.columns:
        pivot_data = pivot_data.drop(columns=['index'])

    # 重命名vt_list为Offset
    pivot_data.rename(columns={'vt_list': 'Offset'}, inplace=True)
    pivot_data.rename(columns={'fail_list': 'FBC'}, inplace=True)
    pivot_data.rename(columns={'Condition': 'State'}, inplace=True)
    bestoffset_data.rename(columns={'Condition': 'State'}, inplace=True)

    return pivot_data, bestoffset_data

def raw_scan_data_save_chunk_to_csv(data, filename, output_dir='.', is_first_chunk=True):
    """
    Save chunk data to CSV file with streaming approach

    Args:
        data: DataFrame to save
        filename: Base filename without extension
        output_dir: Output directory
        is_first_chunk: Whether this is the first chunk (write header)

    Returns:
        str: Full file path
    """
    if data is None or data.empty:
        return None

    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)

    filepath = os.path.join(output_dir, f'{filename}.csv')

    try:
        if is_first_chunk:
            # First chunk: create new file with header
            data.to_csv(filepath, index=False, encoding='utf-8', mode='w')
            print(f"Created {filename}.csv with {len(data)} rows")
        else:
            # Subsequent chunks: append without header
            data.to_csv(filepath, index=False, encoding='utf-8', mode='a', header=False)
            print(f"Appended {len(data)} rows to {filename}.csv")

        return filepath

    except Exception as e:
        print(f'Error saving chunk to {filename}.csv: {e}')
        return None

def raw_scan_data_local_fitting(file_path, chunk_size=500000):
    """
    优化版本：读取CSV文件，按指定字段分组，对每组数据应用三次样条插值，
    并用插值结果更新原始数据中的Value列

    参数:
    file_path: CSV文件路径
    chunk_size: 分块大小，默认500000行
    """
    import gc
    import time

    start_time = time.time()
    print("开始处理数据...")

    # 初始化输出文件路径和标志
    output_dir = os.path.dirname(file_path)
    is_first_chunk = True
    chunk_count = 0

    # 分块读取和处理
    print(f"开始分块读取，每块 {chunk_size} 行")

    try:
        dtype= {
            # Low-cardinality string columns -> 'category'
            'ReadType': 'category',
            'Partial_Prog': 'category',
            'PEC': 'category',
            'WLmode': 'category',
            'WLType': 'category',
            'Condition': 'category',
            'Measurement': 'category',
            # 'State': 'category',

            # High-cardinality or standard numeric columns -> Precise integer types
            stress_col: 'int8',
            'Temperature': 'float32',
            'Channel': 'int8',
            'Ce': 'int8',
            'Lun': 'int8',
            'Block': 'int32',      # CRITICAL: Must be integer to avoid groupby issues
            'Page': 'int32',       # CRITICAL: Must be integer to avoid groupby issues
            'WordLine': 'int32',
            'Level': 'int8',
            'WLGroup': 'int8',
            # 'Offset': 'int8',
            # 'FBC': 'float32',
            
            # Columns requiring special handling -> 'object'
            'Value': 'object',       # CRITICAL: Must be object for .str.split()
        }

        # 分块读取数据
        chunk_reader = pd.read_csv(file_path, chunksize=chunk_size, dtype=dtype)

        # 定义分组键和需要的列
        group_cols = [stress_col, 'Temperature', 'Partial_Prog', 'PEC', 
                    'Channel', 'Ce', 'Lun', 'Block', 'Page', 'WordLine', 'Level', 
                    'WLmode', 'WLType', 'WLGroup', 'Condition']
        required_cols = group_cols + ['Measurement', 'Value']

        for chunk_data in chunk_reader:

            chunk_count += 1
            print(f"处理第 {chunk_count} 块，包含 {len(chunk_data)} 行")

            # 处理当前块
            fitting_chunk, bestoffset_chunk = raw_scan_data_process_chunk(chunk_data, group_cols, required_cols)
            print(f"第 {chunk_count} 块处理完成，数据形状: {fitting_chunk.shape}")
            del chunk_data
            gc.collect()

            # 直接输出处理后的块到文件
            if not fitting_chunk.empty:
                raw_scan_data_save_chunk_to_csv(fitting_chunk, 'raw_scan_data_local_fitting', output_dir, is_first_chunk)
                print(f"raw_scan_data_local_fitting保存到文件夹{output_dir}中")                
                del fitting_chunk
                gc.collect()

            if not bestoffset_chunk.empty:
                raw_scan_data_save_chunk_to_csv(bestoffset_chunk, 'raw_scan_data_local_bestoffset', output_dir, is_first_chunk)
                print(f"raw_scan_data_local_bestoffset保存到文件夹{output_dir}中")

                # 计算page min fbc
                page_fbc_chunk = raw_scan_data_calculate_page_min_fbc(bestoffset_chunk)
                if not page_fbc_chunk.empty:
                    # Stream save FBC data immediately
                    raw_scan_data_save_chunk_to_csv(page_fbc_chunk, 'raw_scan_data_local_page_min_fbc', output_dir, is_first_chunk)
                    print(f"raw_scan_data_local_page_min_fbc保存到文件夹{output_dir}中")

                # 清理内存
                del bestoffset_chunk, page_fbc_chunk
                gc.collect()

            is_first_chunk = False

    except Exception as e:
        print(f"读取文件时出错: {str(e)}")
        return pd.DataFrame()

    if chunk_count == 0:
        print("没有数据被处理")
        return pd.DataFrame()

    total_time = time.time() - start_time
    print(f"处理完成！总耗时: {total_time:.2f} 秒")
    print(f"结果已保存至: {output_dir}")
    print(f"共处理了 {chunk_count} 个数据块")

    # 返回空的DataFrame，因为数据已经直接输出到文件
    return pd.DataFrame()

def raw_scan_data_calculate_page_min_fbc(processed_data):
    if processed_data.empty or 'FBC' not in processed_data.columns:
        return pd.DataFrame()

    try:
        # Define page identification columns (unique page identifier)
        page_id_cols = ['Channel', 'Ce', 'Lun', 'Block', 'WordLine', 'Page']

        # Define preserved attribute columns
        preserved_cols = [stress_col, 'Temperature', 'Partial_Prog', 'PEC', 'Level', 'WLmode', 'WLType', 'WLGroup']  

        # Check available columns
        available_page_id_cols = [col for col in page_id_cols if col in processed_data.columns]
        available_preserved_cols = [col for col in preserved_cols if col in processed_data.columns]

        if not available_page_id_cols:
            print("Warning: No page identification columns available")
            return pd.DataFrame()

        # Combine all grouping columns
        all_group_cols = available_page_id_cols + available_preserved_cols

        # calculate page_min_fbc based on no_fitting_data_bestoffset
        # Group by page + preserved attributes, sum minimum FBC across all states
        page_min_fbc = processed_data.groupby(all_group_cols, observed=True)['FBC'].sum().reset_index()

        # 修复浮点数精度问题：对sum()结果进行四舍五入，保持与插值算法一致的两位小数精度
        page_min_fbc['FBC'] = page_min_fbc['FBC'].round(2)

        print(f"Calculated page minimum FBC for {len(page_min_fbc)} pages")

        page_min_fbc['ReadType'] = 'fitting_best'

        return page_min_fbc

    except Exception as e:
        print(f"Error calculating page minimum FBC: {e}")
        # Clean up in case of exception
        # if 'condition_min_fbc' in locals():
        #     del condition_min_fbc
        return pd.DataFrame()
    
def raw_scan_data_get_page_min_fbc(file_path, chunk_size = 615000):
    import gc

    print("开始处理数据...")

    # 初始化输出文件路径和标志
    output_dir = os.path.dirname(file_path)

    try:
        # 读取主数据文件
        chunk_reader = pd.read_csv(file_path, chunksize=chunk_size)
        chunk_count = 0
        is_first_chunk = True

        for chunk_data in chunk_reader:
            chunk_count += 1
            print(f"处理第 {chunk_count} 块，包含 {len(chunk_data)} 行")

            # 处理当前块
            page_fbc_chunk = raw_scan_data_calculate_page_min_fbc(chunk_data)

            if not page_fbc_chunk.empty:
                raw_scan_data_save_chunk_to_csv(page_fbc_chunk, 'raw_scan_data_local_page_min_fbc', output_dir, is_first_chunk)
                print(f"raw_scan_data_local_page_min_fbc保存到文件夹{output_dir}中")

            # 清理内存
            del chunk_data, page_fbc_chunk
            gc.collect()

            is_first_chunk = False

    except Exception as e:
        print(f"读取文件时出错: {str(e)}")
        return pd.DataFrame()

